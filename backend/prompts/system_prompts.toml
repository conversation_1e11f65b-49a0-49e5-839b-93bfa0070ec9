# General system prompts and instructions

[tool_usage]
# Instructions for using various tools
artist_retrieval = "Use the retrieve_artist_data tool to get authentic lyrical content and style information"
mcp_tools_research = "Use any of these tools if they are available: search_internet, search_wikipedia, search_rapper_info, or search_rapper_wikipedia"
style_tools = "Use style tools to get information about rap styles and their characteristics"

[common_instructions]
# Common instructions used across different agents
stay_in_character = "Remember to stay in character throughout the verse and make it sound authentic to the style"
use_authentic_data = "Use the retrieved lyrics as inspiration for flow, wordplay, and style authenticity"
fact_based_content = "Include specific personal attacks and disses based on real facts about the opponent's life, career, or lyrical content"

[error_handling]
# Templates for error scenarios
generation_error = "Error generating verse: {error_message}"
judgment_error = "Exception occurred while judging the round. Try again."
tool_error = "Error using tools: {error_message}"
default_fallback = "Using default content due to generation error"

[formatting]
# Common formatting templates
previous_verses_header = "Previous verses in this battle:"
verse_separator = "\n\n"
rapper_verse_format = "{rapper_name}:\n{content}"

[validation]
# Validation messages and requirements
verse_length_requirement = "Keep the verse between 12-16 lines"
style_requirement = "Ensure the verse matches the specified rap style"
authenticity_requirement = "Maintain authenticity to the rapper's actual style and characteristics"
