[project]
name = "backend"
version = "0.1.0"
description = "RAGERaps - AI Rap Battle Web Application Backend"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "fastapi>=0.115.12",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.2",
    "pydantic>=2.8.0",
    "langchain>=0.3.0",
    "langchain-core>=0.3.0",
    "langchain-community>=0.3.0",
    "langchain-openai>=0.3.0",
    "langgraph>=0.1.0",
    "langsmith>=0.1.0",
    "qdrant-client[fastembed]>=1.8.0",
    "tavily-python>=0.3.0",
    "openai>=1.0.0",
    "httpx>=0.27.0",
    "asyncio>=3.4.3",
    "pydantic-settings>=2.2.0",
    "langchain-mcp-adapters>=0.1.0",
    "pandas>=2.2.3",
    "langchain-qdrant>=0.2.0",
    "chardet>=5.2.0",
    "toml>=0.10.2",
]
